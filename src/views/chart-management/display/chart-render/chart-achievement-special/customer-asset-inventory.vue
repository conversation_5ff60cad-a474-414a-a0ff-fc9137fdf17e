<script setup>
import CustomerAssetsBoxes from './customer-assets-boxes.vue'
import CustomerCategoriesBox from './cusotmer-categories-box.vue'

defineOptions({
  name: 'CustomerAssetInventory'
})
</script>

<template>
  <div class="customer-asset-inventory__wrapper">
    <div class="filter-box__wrapper">
      <customer-assets-filter />
    </div>
    <div class="categories-box__wrapper">
      <customer-categories-box />
    </div>
    <div class="customer-assets-boxes__wrapper">
      <customer-assets-boxes />
    </div>
  </div>
</template>

<style scoped lang="scss">
.customer-asset-inventory__wrapper {
  width: 100%;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  .filter-box__wrapper {
    height: 32px;
    line-height: 32px;
    width: 100%;
    display: flex;
    margin-bottom: 16px;
  }
  .categories-box__wrapper {
    height: 84px;
    width: 100%;
    margin-bottom: 16px;
    overflow-x: auto; /* 只在需要时显示滚动条 */
    box-sizing: border-box;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      height: 6px; /* 减小滚动条高度 */
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(0, 0, 0, 0.5);
      }
    }
  }

  .customer-assets-boxes__wrapper {
    width: 100%;
    height: calc(100% - 142px);
    overflow: auto;
    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      height: 6px; /* 减小滚动条高度 */
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(0, 0, 0, 0.5);
      }
    }
  }
}
</style>
