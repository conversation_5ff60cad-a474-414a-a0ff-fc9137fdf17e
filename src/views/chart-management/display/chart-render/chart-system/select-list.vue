<!-- eslint vue/no-mutating-props: 1 -->

<!--
 * @Description: 查询条件选择器入口
 -->
<template>
  <div class="select-list">
    <!-- 条件查询 ，枚举值的链接-->
    <!-- https://xbb.yuque.com/xbb/vsf9sv/vasv74 -->
    <div class="select-form">
      <template v-if="search.length > 0">
        <!-- 101：时间所有；102:时间没有自定义；103:没有自定义和时间；104仅有月份; 105常用和自定义范围和月份 -->
        <div
          v-if="[101, 102, 103, 104, 105].some((aa) => search.includes(aa))"
          class="select-form__item"
        >
          <span class="select-form__label">{{ $t('form.chooseTime') }}</span>
          <time-selector
            class="select-form__time"
            :filter-param="filterParams.timeFilter"
            :is-bi="isBi"
            :is-fiscal-year="true"
            :search="search"
            :set-default="false"
            :show-setting="{
              custom: [101, 105].some((aa) => search.includes(aa)),
              month: ![103, 105].some((aa) => search.includes(aa)),
              regular: false,
              season: ![104, 105].some((aa) => search.includes(aa)),
              half: ![104, 105].some((aa) => search.includes(aa)),
              annual: ![104, 105].some((aa) => search.includes(aa)),
              range: ![102].some((aa) => search.includes(aa))
            }"
            @saveFilter="saveFilter"
          >
            <span slot="title-lable"></span>
          </time-selector>
        </div>
        <div
          v-if="showTabsName.length && [101, 201, 202, 203].some((aa) => search.includes(aa))"
          class="select-form__item"
        >
          <span class="select-form__label">{{ $t('form.chooseRange') }}</span>
          <!-- <el-select v-model="filterParams.checkedId" multiple :placeholder="$t('placeholder.choosePls',{attr:''})"
            @visible-change="visibleChange"
            @click.native="alert('1')">
          </el-select> -->
          <multi-tag
            class="select-form__multi-select"
            :prop-format="mulitFormat"
            :selected-tag="filterParams.checkedId"
            @click.native="personSelectVisitOpen"
            @tagDelete="tagDelete"
          ></multi-tag>
        </div>
        <!-- 客户（301，302）或者销售线索（1111，1112） - 查询条件 -->
        <div
          v-if="[301, 302, 1111, 1112].some((aa) => search.includes(aa))"
          class="select-form__item"
        >
          <span class="select-form__label">{{ selectName(1) }}</span>
          <el-select
            v-model="filterParams.refId"
            :clearable="search.includes(301) || search.includes(1111)"
          >
            <el-option
              v-if="search.includes(301) || search.includes(1111)"
              :label="$t('label.total')"
              value="0"
            ></el-option>
            <el-option
              v-for="(item, index) in templateObj[selectList]"
              :key="index"
              :label="item.formName"
              :value="item.formId"
            >
            </el-option>
          </el-select>
        </div>
        <div v-if="[1101, 1102].some((aa) => search.includes(aa))" class="select-form__item">
          <span class="select-form__label">{{
            $t('display.chartSystem.opportunityModuleChoose') + ':'
          }}</span>
          <el-select v-model="filterParams.refId">
            <el-option
              v-if="search.includes(1101)"
              :label="$t('label.total')"
              value="0"
            ></el-option>
            <el-option
              v-for="(item, index) in templateObj.opportunityTemplate"
              :key="index"
              :label="item.formName"
              :value="item.formId"
            >
            </el-option>
          </el-select>
        </div>
        <div v-if="[401].some((aa) => search.includes(aa))" class="select-form__item">
          <span class="select-form__label">{{
            $t('display.chartSystem.contractModuleChoose') + ':'
          }}</span>
          <el-select v-model="filterParams.refId" clearable>
            <el-option :label="$t('label.total')" value="0"></el-option>
            <el-option
              v-for="(item, index) in templateObj.contractTemplate"
              :key="index"
              :label="item.formName"
              :value="item.formId"
            >
            </el-option>
          </el-select>
        </div>
        <div v-if="[501, 502].some((aa) => search.includes(aa))" class="select-form__item">
          <span class="select-form__label">{{
            $t('display.chartSystem.orderModuleChoose') + ':'
          }}</span>
          <el-select v-model="filterParams.refId" :clearable="search.includes(501)">
            <el-option v-if="search.includes(501)" :label="$t('label.total')" value="0"></el-option>
            <template v-for="(item, index) in templateObj.workOrderTemplate">
              <!-- 包含“全部”选项时无需过滤，不包含“全部”选项时，需要过滤 -->
              <el-option
                v-if="
                  search.includes(501) ||
                  (search.includes(502) &&
                    (item.isFree == 0 || (item.expectedTime && item.expectedTime > 0)))
                "
                :key="index"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </template>
          </el-select>
        </div>
        <div v-if="search.includes(1301)" class="select-form__item">
          <span class="select-form__label">{{
            $t('display.chartSystem.orderModuleChoose') + ':'
          }}</span>
          <el-select v-model="filterParams.refId" :clearable="search.includes(1301)">
            <el-option
              v-if="search.includes(1301)"
              :label="$t('label.total')"
              value="0"
            ></el-option>
            <template v-for="(item, index) in templateObj.workOrderV2Template">
              <el-option :key="index" :label="item.formName" :value="item.formId"> </el-option>
            </template>
          </el-select>
        </div>
        <div v-if="search.includes(1401)" class="select-form__item">
          <span class="select-form__label">{{
            $t('display.chartSystem.receiptModuleChoose') + ':'
          }}</span>
          <el-select v-model="filterParams.argTwoId" clearable>
            <template v-for="(item, index) in templateObj.receipOrderTemplate">
              <el-option :key="index" :label="item.name" :value="item.formId"> </el-option>
            </template>
          </el-select>
        </div>
        <div v-if="search.includes(1501)" class="select-form__item">
          <span class="select-form__label">{{
            $t('display.chartSystem.projectModuleChoose') + ':'
          }}</span>
          <el-select v-model="filterParams.refId" :clearable="search.includes(1501)">
            <el-option
              v-if="search.includes(1501)"
              :label="$t('label.total')"
              value="0"
            ></el-option>
            <template v-for="(item, index) in templateObj.projectTemplate">
              <el-option :key="index" :label="item.formName" :value="item.formId"> </el-option>
            </template>
          </el-select>
        </div>
        <div v-if="[801].some((aa) => search.includes(aa))" class="select-form__item">
          <span class="select-form__label">{{ $t('display.chartSystem.capitalAccount') }}</span>
          <el-select v-model="filterParams.accountId" clearable filterable @clear="accountClear">
            <el-option
              v-for="(item, index) in templateObj.fundAccountList"
              :key="index"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <div v-if="[802].some((aa) => search.includes(aa))" class="select-form__item">
          <span class="select-form__label">{{ $t('display.chartSystem.billType') }}</span>
          <el-select v-model="filterParams.type" clearable filterable @clear="accountClear">
            <el-option
              v-for="(item, index) in templateObj.defaultComboValue"
              :key="index"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <div v-if="search.includes(701)" class="select-form__item">
          <span class="select-form__label">{{
            $t('display.chartSystem.addressChoose') + ':'
          }}</span>
          <el-select v-model="filterParams.province" clearable>
            <el-option :label="$t('display.chartSystem.nationwide')" value="china"></el-option>
            <el-option
              v-for="(item, index) in chinaProvince"
              :key="index"
              :label="item.name"
              :value="item.name"
            >
            </el-option>
          </el-select>
        </div>
        <!-- 进销商名称搜索 -->
        <div v-if="search.includes(1201)" class="select-form__item">
          <!-- test -->
          <!-- <v-list-filter
            ref="regularFilter"
            :common-filter-explain-list="conditionList"
            :common-conditons="commonConditons"
            :special-filter-params="{ group: { id: 0, type: 1 } }"
            @conditionChange="conditionChange"
          /> -->
          <!-- test -->
        </div>
        <!-- 坐席选择 -->
        <div v-if="search.includes(1901)" class="select-form__item">
          <span class="select-form__label">{{ $t('display.chartSystem.agentChoose') + ':' }}</span>
          <el-select v-model="filterParams.seatsIdList" clearable filterable multiple>
            <el-option
              v-for="(item, index) in templateObj.seatsIdTemplate"
              :key="index"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <!-- 技能组选择 -->
        <div v-if="search.includes(1902)" class="select-form__item">
          <span class="select-form__label">{{
            $t('display.chartSystem.skillGroupChoose') + ':'
          }}</span>
          <el-select v-model="filterParams.skillGroupIdList" clearable filterable multiple>
            <el-option
              v-for="(item, index) in templateObj.skillGroupTemplate"
              :key="index"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <!-- 客服选择 -->
        <div v-if="search.includes(1903)" class="select-form__item">
          <span class="select-form__label">{{
            $t('display.chartSystem.customerServiceChoose') + ':'
          }}</span>
          <el-select v-model="filterParams.customerServiceIdList" clearable filterable multiple>
            <el-option
              v-for="(item, index) in templateObj.customerServiceTemplate"
              :key="index"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <!-- 门户选择 -->
        <div v-if="search.includes(1904)" class="select-form__item">
          <span class="select-form__label">{{ $t('display.chartSystem.portalChoose') + ':' }}</span>
          <el-select v-model="filterParams.refId" clearable>
            <el-option :label="$t('label.total')" value="0"></el-option>
            <el-option
              v-for="(item, index) in templateObj.portalTemplate"
              :key="index"
              :label="item.formName"
              :value="item.formId"
            >
            </el-option>
          </el-select>
        </div>
        <div v-if="[601].some((aa) => search.includes(aa))" class="select-form__item">
          <products-search
            :field-info="{
              fieldType: 10023,
              attrName: $t('display.chartSystem.productClassChoose'),
              editable: true
            }"
            @getFieldValue="productChange"
          >
          </products-search>
        </div>
        <div v-if="[602, 1121].some((aa) => search.includes(aa))" class="select-form__item">
          <span class="select-form__label">{{ selectName(0) }}</span>
          <el-select
            v-model="filterParams.refId"
            clearable
            filterable
            remote
            :remote-method="remoteMethod"
            @clear="clearModel"
          >
            <template v-if="marketOrProduct">
              <el-option
                v-for="item in modelList"
                :key="item.productId"
                :label="item.productInfo"
                :value="item.productId"
              >
              </el-option>
            </template>
            <template v-else>
              <el-option
                v-for="item in modelList"
                :key="item.id"
                :label="item.marketName"
                :value="item.id"
              >
              </el-option>
            </template>
          </el-select>
        </div>
        <div v-if="search.includes(603)" class="select-form__item">
          <span class="select-form__label">{{ $t('display.chartSystem.fuzzySearch') + ':' }}</span>
          <el-input
            v-model="filterParams.nameLike"
            class="select-form__multi-select"
            clearable
            :placeholder="$t('display.chartSystem.inputNameOrNumber')"
          ></el-input>
        </div>
        <div v-if="search.includes(901)" class="select-form__item">
          <span class="select-form__label">{{ $t('display.chartSystem.customer') }}</span>
          <el-input
            v-model="filterParams.nameLike"
            class="select-form__multi-select"
            clearable
            :placeholder="$t('placeholder.customerNameOrCustomerPhoneSearch')"
          ></el-input>
        </div>
        <div v-if="search.includes(1001)" class="select-form__item">
          <span class="select-form__label">{{ $t('display.chartSystem.provider') }}</span>
          <el-input
            v-model="filterParams.nameLike"
            class="select-form__multi-select"
            clearable
            :placeholder="$t('placeholder.supplierNameSearch')"
          ></el-input>
        </div>
        <!-- 阶段选择(级联选择器) -->
        <div v-if="[1104, 1105].some((item) => search.includes(item))" class="select-form__item">
          <span class="select-form__label">{{ $t('display.chartSystem.stageSelection') }}</span>
          <el-cascader
            :key="'stage' + filterParams.refId"
            v-model="selectStageList"
            collapse-tags
            :disabled="!filterParams.refId || filterParams.refId === '0'"
            :options="stageList"
            :placeholder="$t('label.total')"
            :props="stageListProps"
          >
          </el-cascader>
        </div>
        <!-- 流程版本选择 303客户漏斗 1113线索漏斗 1103赢单机会转换率趋势分析 1106机会漏斗、阶段变更分析-->
        <div
          v-if="[303, 1113, 1103, 1106].some((item) => search.includes(item))"
          class="select-form__item"
        >
          <span class="select-form__label">{{
            $t('display.chartSystem.stageVersionSelection')
          }}</span>
          <el-select
            v-model="filterParams.argTwoId"
            :clearable="![1106, 303, 1113].some((item) => search.includes(item))"
            :disabled="!filterParams.refId || filterParams.refId === '0'"
            :placeholder="$t('timeSelector.pleaseChoose')"
          >
            <el-option
              v-for="item in versionList"
              :key="item.value"
              :label="item.versionName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <!-- wechat企微群 -->
        <div v-if="search.includes(2301)" class="select-form__item">
          <span class="select-form__label">企微群：</span>
          <el-select v-model="filterParams.refId" clearable>
            <el-option
              v-for="(item, index) in templateObj['wechatTemplate']"
              :key="index"
              :label="item.text"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <!-- 企微待办任务-销售行为分析-任务分析 -->
        <div v-if="search.includes(2402)" class="select-form__item">
          <span class="select-form__label">所属表单：</span>
          <el-select
            v-model="filterParams.refId"
            clearable
            :placeholder="$t('timeSelector.pleaseChoose')"
          >
            <el-option
              v-for="item in taskOptsList.formOptsList"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <!-- SOP任务/执行反馈的筛选 -->
        <div v-if="search.includes(2401)" class="select-form__item">
          <span class="select-form__label">任务来源：</span>
          <el-select
            v-model="filterParams.argTwoId"
            :clearable="true"
            :placeholder="$t('timeSelector.pleaseChoose')"
            @change="taskFormChange"
          >
            <el-option
              v-for="item in taskOptsList.sourceOptsList"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <span class="select-form__label" style="margin-left: 20px">模板任务：</span>
          <el-select
            v-model="filterParams.argStrIn"
            class="sop-template-task"
            :clearable="true"
            collapse-tags
            multiple
            :placeholder="$t('timeSelector.pleaseChoose')"
          >
            <el-option
              v-for="item in taskOptsList.taskIdOptsList"
              :key="item.value"
              :label="item.text"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div v-if="search.includes(106)" class="select-form__item">
          <span class="select-form__label">呼叫时长：</span>
          <DurationFilter v-model="filterParams.durationFilter" />
        </div>
      </template>
      <!-- 经销商发货分析按钮 -->
      <div v-if="showByOrder" class="distributor__chartList__type">
        <el-radio-group v-model="distributorType" @change="distributorTypeChange">
          <el-radio-button :label="0">{{ $t('router.distributor.byOrder') }}</el-radio-button>
          <el-radio-button :label="1">{{ $t('router.distributor.byProduct') }}</el-radio-button>
        </el-radio-group>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint vue/no-mutating-props: 1 */

import TimeSelector from '@/components/date-time/time-selector.vue'
import PersonSelect from '@/components/person-select/index'
import MultiTag from '@/components/select-tree/multi-tag.vue'
import ProductsSearch from '@/components/independentFields'
import DurationFilter from './components/duration-filter.vue'
import { chinaProvince } from '@/constants/common/china-province.js'
import { productVagueSearch } from '@/api/product-class'
import { marketVagueSearch } from '@/api/market-manage'
import { getConditionList } from '@/api/statistics.js'

import {
  getProcessVersionList,
  getProcessStageList,
  getProcessStageListHandle
} from '@/api/stage-process-design.js'
import { getTodoFiltersOptions } from '@/api/scrm/customer-operation/automated-operation.js'

import xbb from '@xbb/xbb-utils'

export default {
  name: 'SelectList',

  components: {
    TimeSelector,
    PersonSelect,
    MultiTag,
    ProductsSearch,
    DurationFilter
  },

  provide() {
    return {
      currentopportunityTemp: this
    }
  },

  props: {
    byOrder: Number,
    // 系统图表的查询条件对象
    filterParams: {
      type: Object,
      default: () => ({})
    },
    // 图表对象
    search: {
      type: Array,
      default: () => []
    },
    // 模版选择对象
    templateObj: {
      type: Object,
      default: () => ({})
    },
    // 全屏是否打开
    fullScreenShow: {
      type: Boolean
    },
    // 是否展示经销商发货分析类型按钮
    showByOrder: {
      type: Boolean,
      default: false
    },
    // 进销商特殊search
    distributorSearch: {
      type: Array,
      default: () => []
    },
    // 选中的分组对象
    selectMenu: {
      type: Object,
      default: () => ({})
    },
    isBi: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      allConditionMap: {},
      conditionList: [],
      // 经销商发货分析类型
      distributorType: 0,
      // province: this.filterParams.province, // 省份
      // 时间选择对象
      // 多选回显值
      mulitFormat: {
        label: 'name'
      },
      // 省份枚举
      chinaProvince: chinaProvince,
      // 组织控件可选择的tab
      showTabsName: [],
      // 组织控件默认显示的tab
      activeNameProp: '',
      // 产品模糊搜索列表
      modelList: [],
      stageList: [], // 阶段选择列表
      selectStageList: [], // 阶段选择选中的值
      stageListProps: {
        multiple: true,
        lazy: true,
        lazyLoad: (node, resolve) => {
          if (!node.value || node.level >= 2) {
            resolve()
          } else {
            this.getStageList(node.value).then((stageList) => {
              resolve(stageList)
            })
          }
        }
      },
      versionList: [], // 版本列表
      taskOptsList: {
        // 任务分析相关搜索项
        sourceOptsList: [], // 任务来源列表
        formOptsList: [], // 所属表单
        taskIdOptsList: [] // 所属表单
      }
    }
  },

  computed: {
    customerOrClue() {
      return this.search.some((item) => {
        return [301, 302].includes(item)
      })
    },
    marketOrProduct() {
      return this.search.some((item) => {
        return item === 602
      })
    },
    selectList() {
      return this.customerOrClue ? 'customerTemplate' : 'clueTemplate'
    }
  },

  watch: {
    byOrder: {
      handler(e) {
        if (e !== this.distributorType) {
          this.distributorType = e
        }
      },
      immediate: true
    },
    distributorSearch: {
      handler(val) {
        this.conditionList = val
      },
      deep: true,
      immediate: true
    },
    'filterParams.refId': {
      // 监听机会模板，当模板不是全部的时候，机会分析下的阶段选择需要根据机会模板去请求列表
      handler(val) {
        // 回执单模板不用清空
        if (this.search.includes(1401)) return
        // 切换模板的时候先把流程版本和阶段选择的值清空
        this.filterParams.argTwoId = ''
        this.versionList = []
        this.selectStageList = []
        if (this.search.includes(2301) && this.filterParams.search[0]) {
          this.filterParams.search[0].groupChatFilter = val
          this.filterParams.refId = val
        }
        const template = this.getVersionListTemplate(val)
        template && this.getVersionList(template)
      }
    },
    selectStageList: {
      // 监听选中的值，处理下格式返回给后端
      handler(val) {
        if (val.length) {
          console.log('?????', val)
          this.filterParams.argStrIn = val.flat().filter((item, index) => {
            return index % 2 !== 0
          })
        } else {
          this.filterParams.argStrIn = []
        }
      }
    },
    search: {
      handler(val) {
        // 模版默认值
        this.init(val)
      },
      deep: true
    },
    templateObj: {
      handler(val) {
        Object.keys(val).length && this.init(this.search)
      },
      deep: true,
      immediate: true
    },
    /* 这里的监听主要是为了多模版的图表调用
     * 系统表被仪表盘调用时 要默认选中一个模版才行
     */
    filterParams: {
      handler(val, oldVal) {
        if (!val.refId && oldVal && oldVal.refId) {
          val.refId = oldVal.refId
        }
      },
      immediate: true
    }
  },

  mounted() {
    if (this.search.includes(602) || this.search.includes(1121)) {
      const val = this.$store.state.chartCenter.nameLike || '' // 拿到模糊查询内容
      this.vagueSearch(val)
    }

    this.sopInit()

    getConditionList({})
      .then(({ result: { conditionList } }) => {
        this.allConditionMap = conditionList
      })
      .catch(() => {})
  },
  methods: {
    init(val) {
      this.activeNameProp = ''
      // 范围
      if (val && val.includes(101) && this.selectMenu.alias === 'friendStatistics') {
        this.showTabsName = ['dept']
      }
      if (val && val.includes(201)) {
        this.showTabsName = ['dept', 'user']
      }
      if (val && val.includes(202)) {
        this.showTabsName = ['dept']
      }
      if (val && val.includes(203)) {
        this.showTabsName = ['user']
        this.activeNameProp = 'user'
      }
      // 回执单模板设置
      if (val && val.includes(1401)) {
        this.$set(this.filterParams, 'argTwoId', 0)
      }
      if (this.activeNameProp === '' && this.showTabsName.length) {
        this.activeNameProp = this.showTabsName[0]
      }

      if (!this.fullScreenShow && Object.keys(this.templateObj).length) {
        if ([301, 401, 501, 1101, 1111, 1301, 1501, 1904].some((aa) => val.includes(aa))) {
          this.$set(this.filterParams, 'refId', '0')
        } else if (val.includes(302)) {
          this.$set(
            this.filterParams,
            'refId',
            xbb._get(this.templateObj, 'customerTemplate[0].formId')
          )
        } else if (val.includes(1112)) {
          this.$set(
            this.filterParams,
            'refId',
            xbb._get(this.templateObj, 'clueTemplate[0].formId')
          )
        } else if (val.includes(1102)) {
          this.$set(
            this.filterParams,
            'refId',
            xbb._get(this.templateObj, 'opportunityTemplate[0].formId')
          )
        } else if (val.includes(502)) {
          const template = this.templateObj.workOrderTemplate.filter((item) => {
            return item.isFree === 0 || (item.expectedTime && item.expectedTime > 0)
          })[0]
          if (template) {
            this.filterParams.refId = template.id
          }
        } else if (val.includes(801)) {
          // 资金账户列表
          this.filterParams.accountId =
            this.templateObj.fundAccountList && this.templateObj.fundAccountList[0].id
        } else if (val.includes(802)) {
          this.filterParams.typeId =
            this.templateObj.defaultComboValue && this.templateObj.defaultComboValue[0].id
        } else if (val.includes(2301)) {
          this.filterParams.search = [{ groupChatFilter: 0 }]
          this.filterParams.refId = 0
        }
      }

      // 兼容一下
      if (this.activeNameProp === '') {
        this.activeNameProp = this.showTabsName[0]
      }
    },
    // 获取当前模板名称
    getCurrentTemplate(search) {
      let template
      switch ([303, 1113, 1103, 1104, 1105, 1106, 1301].find((item) => search.includes(item))) {
        case 303: // 客户
          template = 'customerTemplate'
          break
        case 1113: // 线索
          template = 'clueTemplate'
          break
        case 1301: // 工单
          template = 'workOrderV2Template'
          break
        case 1103:
        case 1106:
        case 1104:
        case 1105: // 机会分析
          template = 'opportunityTemplate'
      }
      return template
    },
    // 获取版本列表
    getVersionList(template) {
      getProcessVersionList({
        formId: template.formId, // 表单模板id 必传
        saasMark: 1, // 1表示为系统表
        archive: 2,
        businessType: template.businessType // 区分SaaS和PaaS流程 PaaS流程此字段为空
      })
        .then(({ result, result: { stageProcessVersionPojoList } }) => {
          if ([1104, 1105].some((item) => this.search.includes(item))) {
            // 阶段选择 1104 新增机会金额与数量分析、PK榜新增机会 1105 机会金额与数量预计趋势分析
            this.stageList = stageProcessVersionPojoList.map((item) => {
              return {
                value: item.id,
                label: item.versionName,
                children: []
              }
            })
          } else {
            // 版本选择
            this.versionList = stageProcessVersionPojoList || []
            let currentVersion
            currentVersion =
              stageProcessVersionPojoList && stageProcessVersionPojoList.find((item) => item.enable)
            if (
              [1103, 303, 1113, 1106].some((item) => this.search.includes(item)) &&
              stageProcessVersionPojoList &&
              !currentVersion
            ) {
              // 当销售漏斗 客户漏斗 线索漏斗、阶段变更分析 没有默认选中的版本(enable都为0)，默认第一个为选中版本
              currentVersion = stageProcessVersionPojoList[0]
            }
            this.filterParams.argTwoId = currentVersion && currentVersion.id
          }
        })
        .catch(({ msg }) => {
          this.$message.error(
            this.$t('message.getError', { name: this.$t('stageProcess.versionList') })
          )
        })
    },
    // 获取某版本下的阶段列表(级联选择器)
    getStageList(id) {
      // 获取新增机会金额与数量分析、PK榜新增机会版本下的阶段列表(级联选择器)
      let requestApi = getProcessStageListHandle
      if (this.search.includes(1104)) {
        requestApi = getProcessStageList
      }
      return new Promise((resolve) => {
        requestApi({
          id: id, // 上个接口返回的版本流程id
          formId: this.filterParams.refId
        }).then(({ result: { stageList } }) => {
          const childrenList = stageList.map((item) => {
            return {
              value: item.code,
              label: item.name,
              leaf: true
            }
          })
          resolve(childrenList)
        })
      })
    },
    /**
     * 经销商产品名称变化
     */
    conditionChange(val) {
      this.$emit('conditionSearch', val)
    },
    /**
     * 经销商发货分析类型变化
     */
    distributorTypeChange(value) {
      this.$emit('distributorTypeChange', value)
    },
    /**
     * @description: 1：客户or线索  0：产品or市场
     * @param {*} type
     * @return {*}
     */
    selectName(type) {
      let msg = ''
      if (type) {
        msg = this.customerOrClue
          ? this.$t('display.chartSystem.moduleChoose')
          : this.$t('display.chartSystem.clueModuleChoose')
      } else {
        msg = this.marketOrProduct
          ? this.$t('display.chartSystem.fuzzySearch')
          : this.$t('marketManage.marketSearch')
      }
      return msg + ':'
    },

    // 删除选中的范围
    tagDelete(tag) {
      const deleteTag = this.filterParams.checkedId.filter((item) => {
        return item.id === tag.id
      })
      this.filterParams.checkedId.splice(this.filterParams.checkedId.indexOf(deleteTag[0]), 1)
    },
    //  时间控件选择回调
    saveFilter(param, isSave, text) {
      console.log(this.$t('nouns.time'), param, isSave, text)
      this.filterParams.timeFilter = JSON.parse(JSON.stringify(param))
      this.$set(this.filterParams, 'timeLabel', text)
    },
    // 资金账户选项清空时参数置0
    accountClear() {
      this.filterParams.accountId = 0
    },
    // 产品分类选择结束
    productChange(obj) {
      console.log(this.$t('display.chartSystem.productClassify'), obj)
      if (
        ['workOrderV2ProductTypeAnalysis', 'workOrderV2ProductAnalysis'].includes(
          this.selectMenu.alias
        )
      ) {
        //工单产品分类
        this.$set(this.filterParams, 'refTwoId', obj.value === -1 ? '' : obj.value)
      } else {
        this.filterParams.refId = obj.value === -1 ? '' : obj.value
      }
    },
    // 产品or市场 模糊搜索
    vagueSearch(nameOrNoLike) {
      const promise = this.marketOrProduct ? productVagueSearch : marketVagueSearch
      promise({ nameOrNoLike })
        .then(({ result }) => {
          this.modelList = this.marketOrProduct ? result.productSearchDetailVOS : result.marketList
        })
        .catch(() => {})
    },
    // 模糊搜索触发事件
    remoteMethod(value) {
      this.$store.commit('SET_NAME_LIKE', value) // 将模糊条件放到vuex，保证自身调用组件可以 使用
      this.vagueSearch(value)
    },
    // 模糊搜索清空
    clearModel() {
      this.vagueSearch()
    },
    personSelectVisitOpen() {
      // 仪表盘全屏时禁止弹出
      if (+utils.SS.get('chart-panel-fullscreen') === 1) {
        return
      }
      const defaultVal = this.filterParams?.checkedId || []
      PersonSelect({
        showTabsName: this.showTabsName,
        defaultVal: defaultVal,
        tabMultiple: true,
        otherParams: {
          // 是否控制组织架构权限范围
          deptVisibleRule: true,
          parentStrictly: true
        }
      }).then((res) => {
        if (res.data) {
          this.$set(this.filterParams, 'checkedId', res.data)
        }
      })
    },
    // *****************************************以下sop相关*************************************
    // 任务分析 所属表单-模板任务筛选关联 所属表单选中项变化-清空模板任务-重新请求模板任务的选择项
    taskFormChange(val) {
      this.filterParams.argStrIn = []
      if (!val) return (this.taskOptsList.taskIdOptsList = [])
      this.getSopFilter({ businessType: val, filterAttr: 'taskId' })
    },
    // sop初始化
    async sopInit() {
      // TODO: 维护关系可提取
      const dataDiff = {
        form: { listKey: 'formOptsList', valueKey: 'refId' },
        source: { listKey: 'sourceOptsList', valueKey: 'argTwoId' },
        taskId: { listKey: 'taskIdOptsList', valueKey: 'argStrIn' }
      }
      const formInfo = { value: 2402, filterAttr: 'form' }
      const sourceInfo = { value: 2401, filterAttr: 'source' }
      const sopUniList = [formInfo, sourceInfo]
      const hasSopUniList = sopUniList.filter((item) => this.search.includes(item.value))
      // 如果是sop图表
      console.log('hasSopUniList.length', hasSopUniList.length)
      if (hasSopUniList.length) {
        // 获取需要默认选中的参数
        if (window.location.href.includes('sopInit')) {
          const sopInitParams = JSON.parse(decodeURIComponent(this.$route.query.sopInit))
          // 选择范围赋值
          this.filterParams.checkedId = sopInitParams.userRange
          for (let i = 0; i < hasSopUniList.length; i++) {
            const item = hasSopUniList[i]
            await this.getSopFilter({ filterAttr: item.filterAttr })
            this.filterParams[dataDiff[item.filterAttr].valueKey] = sopInitParams[item.filterAttr]
          }
          await this.getSopFilter({ filterAttr: 'taskId', businessType: sopInitParams['source'] })
          // TODO:兼容system-single的监听防抖导致的请求丢失
          setTimeout(() => {
            this.filterParams['argStrIn'] = sopInitParams['taskId']
          }, (this.$attrs.index + 1) * 750)
          if (this.$attrs.index + 1 === this.$attrs['data-list-length']) {
            history.replaceState(null, null, window.location.href.replace(/\?\S{1,}/g, ''))
          }
        } else {
          hasSopUniList.forEach((item) => {
            this.getSopFilter({ filterAttr: item.filterAttr })
          })
          console.log('this.filterParams.argTwoId', this.filterParams.argTwoId)
          // 全屏显示需要请求数据
          if (this.filterParams.argTwoId) {
            this.getSopFilter({ filterAttr: 'taskId', businessType: this.filterParams.argTwoId })
          }
        }
      }
    },
    // sop筛选项获取
    async getSopFilter(params) {
      // TODO: 维护关系可提取
      const dataDiff = {
        form: { listKey: 'formOptsList', valueKey: 'refId' },
        source: { listKey: 'sourceOptsList', valueKey: 'argTwoId' },
        taskId: { listKey: 'taskIdOptsList', valueKey: 'argStrIn' }
      }
      return new Promise((resolve) => {
        getTodoFiltersOptions(params)
          .then((res) => {
            const list = res.result.list
            this.taskOptsList[dataDiff[params.filterAttr].listKey] = list
            resolve(true)
          })
          .catch(() => {
            resolve(false)
          })
      })
    },
    // 判断是否需要获取阶段版本
    getVersionListTemplate(refId) {
      // 版本筛选 303客户漏斗 1113线索漏斗 阶段选择 1104 新增机会金额与数量分析、PK榜新增机会 1105 机会金额与数量预计趋势分析
      if (
        refId &&
        refId !== '0' &&
        [1103, 303, 1113, 1104, 1105, 1106].some((item) => this.search.includes(item))
      ) {
        const currentTemplateName = this.getCurrentTemplate(this.search)
        const templateArr = this.templateObj[currentTemplateName]
        return templateArr && templateArr.find((item) => item.formId === this.filterParams.refId)
      }
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.select-list {
  .distributor__chartList__type {
    position: absolute;
    right: 30px;
    display: inline-block;
  }
  .select-form {
    display: inline-flex;
    flex-wrap: wrap;
    align-items: flex-end;
    &__item {
      display: inline-block;
      padding-right: 18px;
      padding-bottom: 14px;
      padding-left: 12px;
      // &:first-child {
      //   padding-left: 12px;
      // }
    }
    &__label {
      font-size: 14px;
    }
    &__time {
      padding-top: 7px;
      padding-bottom: 7px;
      border: 1px solid $line-filter;
      border-radius: 3px;
    }
    &__multi-select {
      display: inline-block;
      width: 240px;
      // vertical-align: -webkit-baseline-middle;
    }
  }

  // sop相关的特殊样式
  .sop-template-task {
    :deep(.el-select__tags) {
      display: flex !important;
      flex-wrap: nowrap;
      @include singleline-ellipsis();
    }
  }
}
</style>

<style lang="scss">
.select-list {
  .select-form {
    .time-selector {
      .btn-text {
        border: none;
      }
      .el-select-dropdown.el-popper.is-multiple {
        display: none;
      }
    }
  }
}
</style>
