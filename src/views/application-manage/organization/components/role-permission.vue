/* * @Description: 角色权限分配 */

<template>
  <div class="role-permission">
    <el-dialog :before-close="handleClose" fullscreen :visible.sync="dialogFormVisible">
      <div slot="title">
        <span class="el-dialog__title" v-html="'分配角色 - ' + addNodeByWX(item.name)"></span>
      </div>
      <div class="role-permission__wrap">
        <!-- 所选角色 -->
        <div
          class="role-permission__item"
          :class="showPermissionSet ? 'all-role-left' : 'all-role'"
        >
          <div v-if="isShowTips" class="role-permission__item--tips">
            <div>该员工未授权账户，即使分配包含授权账户相关的角色，也无法生效。</div>
          </div>
          <h4 class="role-permission__item--title">{{ $t('organization.chooseRole') }}</h4>
          <person-select
            v-if="isSelectRoleShow"
            ref="rolePermissionSelect"
            active-tab-name="role"
            :default-val="defaultRoleIds"
            :fee-list="feeList"
            is-filter-role
            :is-multi-fee="isMultiFee"
            :is-role-permission="isRolePermission"
            :permission-type="permissionType"
            :show-tabs-name="['role']"
            :sub-admin="true"
            :without-dialog="true"
            @change="handleRoleChange"
            @changePermissionType="changePermissionType"
          >
          </person-select>
        </div>

        <!-- 角色权限范围 -->
        <div v-if="showPermissionSet" class="role-permission__item">
          <h4 class="role-permission__item--title">{{ $t('organization.limitRange') }}</h4>
          <p class="role-permission__item--desc">{{ $t('organization.limitRangeTips') }}</p>
          <template>
            <person-select
              active-tab-name="dept"
              :default-val="rolePermission"
              :fee-list="feeList"
              :is-multi-fee="isMultiFee"
              :is-role-permission="isRolePermission"
              :show-tabs-name="['dept', 'user']"
              :sub-admin="true"
              :without-dialog="true"
              @change="handlePermissionChange"
            >
            </person-select>
            <div class="role-permission__item--footer">
              <el-button :disabled="loading" type="primary" @click="savePermission">{{
                $t('operation.save')
              }}</el-button>
            </div>
          </template>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import PersonSelect from '@/components/person/person-select'
import {
  userAddRole,
  userDelRole,
  maxDataPermission,
  updateAssist,
  addPermissionGroup,
  deletePermissionUserGroup
} from '@/api/system'
import xbbError from '@xbb/xbb-error'

export default {
  name: 'AssignRole',

  components: {
    PersonSelect
  },

  props: {
    item: {
      type: Object,
      required: true
    },
    isRolePermission: {
      type: Boolean,
      default: false
    },
    isMultiFee: {
      type: Boolean,
      default: false
    },
    feeList: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      dialogFormVisible: false,
      // 角色列表
      roleList: [],
      loading: false,
      // 该员工默认角色id
      defaultRoleIds: this.item.roleSet.map((item) => {
        return {
          id: item.id,
          editable: item.editable === 0 ? item.editable : 1,
          name: item.roleName,
          property: 'role'
        }
      }),
      // 最高数据权限
      dataPermission: '', // 1本人，2部门，3部门及下属部门，4自定义角色、助理，5全公司
      // 角色权限
      rolePermission: [],
      // 是否显示角色选择组件
      isSelectRoleShow: true,
      showPermissionSet: false,
      permissionType: 'role'
    }
  },
  computed: {
    // 是否显示角色权限分配
    isShowPermissionEdit() {
      return true
    },
    isShowTips() {
      // 改方法与 src/views/application-manage/organization/fee-operate.js 内的showLicenseName 基本一致。
      const val = this.item.licenseArray
      const licenseNameObj = {}
      const feeNameArr = []
      this.feeList.forEach((item) => {
        licenseNameObj[item.feeType] = item.feeName
      })
      const getName = (value) => {
        Object.keys(value).forEach((key) => {
          if (value[key]) {
            feeNameArr.push(licenseNameObj[key])
          }
        })
        return feeNameArr.length ? false : true
      }
      const str = Object.keys(val).length ? getName(val) : false
      return str
    }
  },
  mounted() {
    // 若包含权限组角色，把权限组角色放到默认选中角色中
    if (this.item.permissionGroup) {
      this.defaultRoleIds = this.defaultRoleIds.concat(
        this.item.permissionGroup.roleList.map((item) => {
          return {
            id: item.id,
            editable: item.editable,
            name: item.name,
            property: 'roleGroup'
          }
        })
      )
    }
    this.init()
  },

  methods: {
    // 初始化
    init() {
      this.dialogFormVisible = true
      this.maxDataPermission(this.item.userId)
    },
    handleClose() {
      this.$emit('close')
    },
    changePermissionType(val) {
      this.permissionType = val
    },
    // 监听角色变动
    handleRoleChange(selection, type, items = []) {
      this.permissionType = items[0].property

      const params = {
        targetUserId: this.item.userId
      }
      if (this.permissionType === 'role') {
        params.roleId = items.map((role) => role.id)
      } else {
        params.permissionGroupId = items[0].id
        params.targetUserName = this.item.name
      }
      const nameStr = items.map((role) => role.name).join(' / ')
      switch (type) {
        case 'add':
          this.$confirm(`确定要为该用户添加“${nameStr}”角色吗?`, this.$t('organization.addRole'), {
            confirmButtonText: this.$t('operation.confirm'),
            cancelButtonText: this.$t('operation.cancel')
          })
            .then(() => {
              if (this.permissionType === 'role') {
                this.userAddRole(params, selection)
              } else {
                this.userAddRoleGroup(params, selection)
              }
            })
            .catch(() => {
              this.resetRole()
            })
          break
        case 'delete':
          this.$confirm(`您确定要删除该用户的“${nameStr}”角色？`, this.$t('organization.delRole'), {
            confirmButtonText: this.$t('operation.confirm'),
            cancelButtonText: this.$t('operation.cancel')
          })
            .then(() => {
              if (this.permissionType === 'role') {
                this.userDelRole(params, selection)
              } else {
                this.userDelRoleGroup(params, selection)
              }
            })
            .catch(() => {
              this.resetRole()
            })
          break
      }
    },

    // 监听数据权限的变化
    handlePermissionChange(selection) {
      this.rolePermission = selection
    },

    // 重置用户角色
    resetRole() {
      this.isSelectRoleShow = false
      this.$nextTick(() => {
        this.isSelectRoleShow = true
      })
    },
    // 重置部门/成员
    resetUser() {
      this.showPermissionSet = false
      this.$nextTick(() => {
        this.showPermissionSet = true
      })
    },
    // 为员工添加角色
    userAddRole(params, selection) {
      userAddRole(params)
        .then((res) => {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.defaultRoleIds = selection
          this.$emit('refresh')
          this.maxDataPermission(this.item.userId)
        })
        .catch((res) => {
          this.resetRole()
        })
    },
    userAddRoleGroup(params, selection) {
      addPermissionGroup(params)
        .then((res) => {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.defaultRoleIds = selection
          this.$emit('refresh')
          this.maxDataPermission(this.item.userId)
        })
        .catch((res) => {
          this.resetRole()
        })
    },
    userDelRoleGroup(params, selection) {
      deletePermissionUserGroup(params)
        .then((res) => {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.defaultRoleIds = selection
          this.$emit('refresh')
          this.maxDataPermission(this.item.userId)
        })
        .catch((res) => {
          this.resetRole()
        })
    },

    // 为员工删除角色
    userDelRole(params, selection) {
      userDelRole(params)
        .then((res) => {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.defaultRoleIds = selection
          this.$emit('refresh')
          this.maxDataPermission(this.item.userId)
        })
        .catch((res) => {
          this.resetRole()
          xbbError.errorCatch(res, 2)
        })
    },

    // 获取员工最大的数据权限
    maxDataPermission(targetUserId) {
      maxDataPermission({ targetUserId })
        .then((res) => {
          if (res.result.assistDepList || res.result.groupDepList) {
            const depList =
              res.result.assistDepList?.map((item) => {
                return {
                  id: item.id,
                  name: item.name,
                  property: 'dept',
                  editable: item.editable === 0 ? item.editable : 1
                }
              }) || []
            const userList =
              res.result.assistUserList?.map((item) => {
                return {
                  id: item.userId,
                  name: item.name,
                  property: 'user',
                  editable: item.editable === 0 ? item.editable : 1
                }
              }) || []
            const groupDepList =
              res.result.groupDepList?.map((item) => {
                return {
                  id: item.id,
                  name: item.name,
                  editable: 0,
                  property: 'dept',
                  disabled: true
                }
              }) || []
            const groupUserList =
              res.result.groupUserList?.map((item) => {
                return {
                  id: item.id,
                  name: item.name,
                  editable: 0,
                  property: 'user',
                  disabled: true
                }
              }) || []
            // 权限组包含的部门替换普通部门
            for (let i = 0; i < groupDepList.length; i++) {
              depList.forEach((item, index) => {
                if (item.id === Number(groupDepList[i].id)) {
                  depList.splice(index, 1)
                }
              })
            }
            for (let i = 0; i < groupUserList.length; i++) {
              userList.forEach((item, index) => {
                if (item.id === groupUserList[i].id) {
                  userList.splice(index, 1)
                }
              })
            }
            this.rolePermission = depList
              .concat(userList)
              .concat(groupDepList)
              .concat(groupUserList)
            this.showPermissionSet = true
            this.resetUser()
          } else {
            this.rolePermission = []
            this.showPermissionSet = false
          }
          // this.dataPermission = res.result.dataPermission
          // if (this.dataPermission === 3 || this.dataPermission === 2) {
          //   this.rolePermission = res.result.manageDepList
          // // 自定义角色
          // } else if (this.dataPermission === 4) {
          //   const depList = res.result.assistDepList.map(item => {
          //     return {
          //       id: item.id,
          //       name: item.name,
          //       property: 'dept',
          //       editable: 1
          //     }
          //   })
          //   const userList = res.result.assistUserList.map(item => {
          //     return {
          //       id: item.userId,
          //       name: item.name,
          //       property: 'user',
          //       editable: 1
          //     }
          //   })
          //   this.rolePermission = depList.concat(userList)
          // } else {
          //   this.rolePermission = []
          // }
        })
        .catch(() => {})
    },

    // 保存所选的权限范围
    savePermission() {
      this.loading = true
      const params = {
        targetUserId: this.item.userId,
        assistTuple: this.rolePermission.map((item) => {
          return {
            assistType: item.property === 'user' ? 1 : 2, // 1表示员工，2表示部门
            assistTypeValue: item.id + ''
          }
        })
      }
      updateAssist(params)
        .then((res) => {
          this.$message({
            type: 'success',
            message: res.msg
          })
        })
        .finally(() => {
          this.loading = false
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.role-permission {
  :deep(.el-dialog__body) {
    position: relative;
    display: flex;
    justify-content: center;
    height: calc(100% - 57px);
    padding: 0;
    overflow-x: scroll;
    background-color: $neutral-color-1;
  }
  .role-permission__wrap {
    position: relative;
    display: flex;
    gap: 24px;
    justify-content: space-between;
    width: 1104px; // 加上padding 1200
    height: calc(100% - 64px);
    padding: 32px 48px;
    overflow-x: scroll;
    background-color: $base-white;
  }
  .role-permission__item {
    display: flex;
    flex: 0 0 500px;
    flex-direction: column;
    width: 500px;
    .role-permission__item--tips {
      padding: 12px 30px;
      margin-bottom: 24px;
      font-size: 12px;
      font-weight: normal;
      line-height: 16px;
      color: $text-plain;
      background: $neutral-color-1;
      border-radius: 4px;
    }
    .role-permission__item--title {
      padding-bottom: 20px;
      font-size: 16px;
      color: $text-main;
    }
    .role-permission__item--desc {
      padding: 12px 16px;
      margin-bottom: 12px;
      font-size: 12px;
      color: $text-plain;
      background: $neutral-color-1;
    }
    .role-permission__item--footer {
      padding-top: 10px;
      text-align: left;
    }
  }
  .all-role-left {
    flex: 0 0 580px;
  }
  .all-role {
    flex: 1;
  }
  &__tag {
    font-size: 0;
    &--item {
      box-sizing: border-box;
      display: inline-block;
      min-width: 68px;
      height: 23px;
      padding: 0 10px;
      margin-right: 8px;
      margin-bottom: 12px;
      font-size: 12px;
      line-height: 23px;
      color: $text-auxiliary;
      text-align: center;
      background-color: $neutral-color-1;
      border-radius: 2px;
    }
  }
  &__tips {
    font-size: 13px;
    line-height: 1.6;
    color: $text-auxiliary;
  }
}
</style>
