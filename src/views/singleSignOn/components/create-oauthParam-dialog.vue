<!--
 * @Description: 新建单点登录设置参数弹窗
-->
<template>
  <div>
    <el-dialog
      :before-close="closeDialog"
      custom-class="create-dataset-dialog"
      :title="`${isEdit ? $t('operation.edit') : $t('operation.add')}映射`"
      :visible.sync="dialogShow"
      width="50%"
    >
      <div class="reate-dataset-content">
        <el-form
          ref="dataSetForm"
          label-position="left"
          label-width="140px"
          :model="dataSetForm"
          :rules="rules"
        >
          <el-form-item
            v-if="stageType === 1"
            :label="$t('singleSignOn.parameterPosition') + ':'"
            prop="position"
          >
            <el-select
              v-model="dataSetForm.position"
              :placeholder="
                $t('placeholder.inputPls', { attr: $t('singleSignOn.parameterPosition') })
              "
            >
              <el-option
                v-for="item in position"
                :key="item.label"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="stageType === 2"
            :label="$t('singleSignOn.xbbParams') + ':'"
            prop="attr"
          >
            <el-input
              v-model.trim="dataSetForm.attr"
              :disabled="true"
              name="attr"
              :placeholder="$t('placeholder.inputPls', { attr: $t('singleSignOn.xbbParams') })"
            ></el-input>
            <div class="form-item_tool">
              <div class="item_tool_flex">
                <div class="item_tool_but">
                  <el-cascader
                    clearable
                    :options="fieldOptions"
                    :props="{ label: 'name', value: 'attr' }"
                    :show-all-levels="false"
                    @change="handleChangeKey"
                  >
                    <template slot-scope="{ node, data }">
                      <span>{{ data.name }}</span>
                      <span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
                      <span v-if="node.isLeaf">
                        {{ data.description !== '' ? '' : `(${data.description})` }}
                      </span>
                    </template>
                  </el-cascader>
                  <span>{{ $t('singleSignOn.insertField') }}</span>
                </div>
              </div>
            </div>
            <!-- <div>
              <span class="demonstration">默认 click 触发子菜单</span>
              <el-cascader v-model="dataSetForm.attr" :options="options"></el-cascader>
            </div> -->
          </el-form-item>
          <el-form-item
            v-if="stageType === 2"
            :label="'IDP' + $t('singleSignOn.params')"
            prop="standardAttr"
          >
            <el-input
              v-model.trim="dataSetForm.standardAttr"
              :placeholder="$t('placeholder.inputPls', { attr: 'IDP' + $t('singleSignOn.params') })"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="stageType === 1" :label="$t('singleSignOn.params') + ':'" prop="attr">
            <el-input
              v-model.trim="dataSetForm.attr"
              :placeholder="$t('placeholder.inputPls', { attr: 'IDP' + $t('singleSignOn.params') })"
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="stageType === 1"
            :label="$t('singleSignOn.parameterValue') + ':'"
            prop="standardAttr"
          >
            <el-input
              v-model.trim="dataSetForm.standardAttr"
              :disabled="true"
              name="value"
              :placeholder="
                $t('placeholder.inputPls', { attr: 'IDP' + $t('singleSignOn.parameterValue') })
              "
            ></el-input>
            <div class="form-item_tool">
              <div class="item_tool_flex">
                <div class="item_tool_but">
                  <el-cascader
                    clearable
                    :options="fieldOptions"
                    :props="{ label: 'name', value: 'attr' }"
                    :show-all-levels="false"
                    @change="handleChange"
                  >
                    <template slot-scope="{ node, data }">
                      <span>{{ data.name }}</span>
                      <span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
                      <span v-if="node.isLeaf">
                        {{ data.description == '' ? '' : `(${data.description})` }}
                      </span>
                    </template>
                  </el-cascader>
                  <span>{{ $t('singleSignOn.insertField') }}</span>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item :label="$t('homeManage.describe')" prop="description">
            <el-input
              v-model.trim="dataSetForm.description"
              :placeholder="$t('placeholder.inputPls', { attr: $t('homeManage.describe') })"
              :rows="5"
              type="textarea"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="reate-dataset-footer">
        <el-button @click="closeDialog">{{ $t('operation.cancel') }}</el-button>
        <el-button type="primary" @click="submit">{{ $t('operation.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { oauthCustomParamFieldlist } from '@/api/oauthParam'

export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    value: {
      type: Object,
      default: () => {}
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    stepId: {
      type: Number,
      default: 1
    },
    stageType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      queryID: this.$route.query.id,
      originForm: {
        id: '',
        name: '',
        memo: ''
      },
      FieldValue: [],
      fieldOptions: [
        {
          attr: 'customList',
          name: this.$t('formDesign.customField'),
          children: []
        },
        {
          attr: 'systemList',
          name: this.$t('formDesign.systemField'),
          children: []
        }
      ],
      dataSetForm: {
        connectorId: this.$route.query.id,
        enable: 1,
        stepId: this.stepId,
        stageType: this.stageType,
        description: '',
        position: 1,
        attr: '',
        standardAttr: ''
      },
      position: [
        {
          label: 'BODY',
          value: 1
        },
        {
          label: 'HEADER',
          value: 2
        }
      ],

      dialogShow: this.show,
      rules: {
        position: [
          {
            required: true,
            message: '',
            trigger: 'blur'
          }
        ],
        attr: [
          {
            required: true,
            message: `缺少${
              this.stageType === 1
                ? this.$t('singleSignOn.params')
                : this.$t('singleSignOn.xbbParams')
            }`,
            trigger: 'blur'
          }
        ],
        standardAttr: [
          {
            required: true,
            message: `缺少${
              this.stageType === 1
                ? this.$t('singleSignOn.parameterValue')
                : 'IDP' + this.$t('singleSignOn.params')
            }`,
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created() {
    if (JSON.stringify(this.value) !== '{}') {
      this.dataSetForm = this.value
      this.dataSetForm.enable = this.value.enable.value
      this.dataSetForm.position = this.value.position ? this.value.position.value : 1
      this.dataSetForm.stageType = this.value.stageType.value
    }
    this.oauthCustomParamFieldlist() // 获取字段
  },
  methods: {
    // 关闭弹窗
    closeDialog() {
      this.$emit('close', false)
    },
    async oauthCustomParamFieldlist() {
      const { result } = await oauthCustomParamFieldlist({
        connectorId: this.queryID
      })
      this.fieldOptions[0].children = result.customList
      this.fieldOptions[1].children = result.systemList
    },
    // 插入字段
    handleChange(value) {
      // console.log(value)
      this.dataSetForm.standardAttr = ''
      if (value.length === 0) return
      // 获取textarea标签
      const field = document.getElementsByName('value')[0]
      // textarea光标开始位置
      const startPos = field.selectionStart
      // 获取之前textarea中的内容
      const datavalue = this.dataSetForm.standardAttr
      // 光标插入位置前的内容
      const startContent = datavalue || datavalue.substring(0, startPos)
      // 将插入后完整的内容重新赋值给texrarea
      this.dataSetForm.standardAttr = `${startContent}${value[1]}`
      this.FieldValue = []
    },
    // 插入key字段
    handleChangeKey(value) {
      // console.log(value)
      this.dataSetForm.attr = ''
      if (value.length === 0) return
      // 获取textarea标签
      const field = document.getElementsByName('attr')[0]
      // textarea光标开始位置
      const startPos = field.selectionStart
      // 获取之前textarea中的内容
      const datavalue = this.dataSetForm.attr
      // 光标插入位置前的内容
      const startContent = datavalue || datavalue.substring(0, startPos)
      // 将插入后完整的内容重新赋值给texrarea
      this.dataSetForm.attr = `${startContent}${value[1]}`
      this.FieldValue = []
    },
    submit() {
      if (this.dataSetForm.attr === '') {
        this.$message.error(
          `缺少${
            this.stageType === 1
              ? this.$t('singleSignOn.params')
              : this.$t('singleSignOn.xbbParams')
          }`
        )
        return
      }
      if (this.dataSetForm.standardAttr === '') {
        this.$message.error(
          `缺少${
            this.stageType === 1
              ? this.$t('singleSignOn.parameterValue')
              : 'IDP' + this.$t('singleSignOn.params')
          }`
        )
        return
      }
      this.$emit('submit', this.dataSetForm)
      // this.closeDialog()
    }
  }
}
</script>

<style lang="scss">
.create-dataset-dialog {
  .el-input {
    width: 80%;
  }
  .el-form-item__label:before {
    // position: absolute;
    left: 65px;
  }
}
</style>

<style lang="scss" scoped>
.create-dataset-dialog {
  .reate-dataset-content {
    min-height: 220px;
  }
  .reate-dataset-footer {
    padding-top: 15px;
    text-align: right;
    border-top: 1px solid rgba(0, 0, 0, 0.09);
  }
  .inline-box {
    display: inline-block;
  }
  .block-padding {
    padding-top: 5px;
    padding-left: 67px;
    .el-date-editor.el-input,
    .el-date-editor.el-input__inner {
      width: 390px;
    }
  }
  .mt-5 {
    margin-top: 5px;
  }
  .form-item_tool {
    display: flex;
    justify-content: space-between;
    color: $brand-color-4;
    .item_tool_flex {
      display: flex;
      .item_tool_but {
        width: 100px;
      }
    }
  }
}
.el-cascader {
  position: absolute;
  width: 100px;
  :deep(.el-input) {
    visibility: hidden;
  }
}
</style>
